{
	"easycom": {
		"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [{
			"path": "pages/tail",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom" //单个页面设置
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "多样青春·活动",
				"navigationStyle": "custom" //单个页面设置，

			}
		},
		{
			"path": "pages/fenl/index",
			"style": {
				"navigationBarTitleText": "分类",
				"navigationStyle": "custom", //单个页面设置
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/index/Apply",
			"style": {
				"navigationBarTitleText": "申请入驻"
			}
		},

		{
			"path": "pages/activity/index",
			"style": {
				"navigationBarTitleText": "活动中心",
				"navigationStyle": "custom", //单个页面设置
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/activity/stationStemys",
			"style": {
				"navigationBarTitleText": "系统消息",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/my/index",
			"style": {
				"navigationBarTitleText": "我的", // 更改为更清晰的标题
				"navigationStyle": "custom" //单个页面设置
			}
		},
		{
			"path": "pages/my/applyOwner",
			"style": {
				"navigationBarTitleText": "主理人申请", // 更改为更清晰的标题
				"navigationStyle": "custom" //单个页面设置
			}
		},
		{
			"path": "pages/my/classCheck",
			"style": {
				"navigationBarTitleText": "活动核销", // 更改为更清晰的标题
				"navigationStyle": "custom" //单个页面设置
			}
		},
		{
			"path": "pages/center/index", // 确保此处与 tabBar 匹配
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom" //单个页面设置
			}
		}

	],
	"subPackages": [{
			"root": "packageA",
			"pages": [{
					"path": "afterSales/index",
					"style": {
						"navigationBarTitleText": "售后订单"
					}
				},

				{
					"path": "afterSales/list",
					"style": {
						"navigationBarTitleText": "售后订单"
					}
				},
				{
					"path": "afterSales/info",
					"style": {
						"navigationBarTitleText": "售后订单"
					}
				},
				{
					"path": "Negotiate/agreement",
					"style": {
						"navigationBarTitleText": "用户协议"
					}
				},
				{
					"path": "Negotiate/privacy",
					"style": {
						"navigationBarTitleText": "隐私政策"
					}
				},
				{
					"path": "my/person-detail",
					"style": {
						"navigationBarTitleText": "个人中心"
					}
				},

				{
					"path": "my/makeList",
					"style": {
						"navigationBarTitleText": "我的预约"
					}
				},
				{
					"path": "my/station",
					"style": {
						"navigationBarTitleText": "站内信"
					}
				},
				{
					"path": "my/orderList",
					"style": {
						"navigationBarTitleText": "我发布的活动",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/exercise",
					"style": {
						"navigationBarTitleText": "我参与的活动",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/pendinPay",
					"style": {
						"navigationBarTitleText": "待支付订单"
					}
				},
				{
					"path": "my/pendinPayDetail",
					"style": {
						"navigationBarTitleText": "待核销订单"
					}
				},
				{
					"path": "my/pendinPayStatus",
					"style": {
						"navigationBarTitleText": "已完成订单",
						"navigationStyle": "custom"
					}
				},

				{
					"path": "my/cancel",
					"style": {
						"navigationBarTitleText": "课程核销"
					}
				},
				{
					"path": "my/cancelActivity",
					"style": {
						"navigationBarTitleText": "活动核销"
					}
				},
				{
					"path": "my/success",
					"style": {
						"navigationBarTitleText": "报名成功"
					}
				},
				{
					"path": "my/classCheck",
					"style": {
						"navigationBarTitleText": "报名审核"
					}
				},
				{
					"path": "my/stationDetail",
					"style": {
						"navigationBarTitleText": "消息详情"
					}
				},
				{

					"path": "my/Feedback",
					"style": {
						"navigationBarTitleText": "课程反馈"
					}
				},
				{

					"path": "my/timetable",
					"style": {
						"navigationBarTitleText": "课程表"
					}
				},
				{

					"path": "my/Judge",
					"style": {
						"navigationBarTitleText": "课程评价"
					}
				},
				{
					"path": "my/settleIn",
					"style": {
						"navigationBarTitleText": "邀请入驻"
					}
				},
				// {
				// 	"path": "my/management",
				// 	"style": {
				// 		"navigationBarTitleText": "课程管理"
				// 	}
				// },
				// {
				// 	"path": "my/ac-management",
				// 	"style": {
				// 		"navigationBarTitleText": "活动管理"
				// 	}
				// },
				{
					"path": "my/help",
					"style": {
						"navigationBarTitleText": "帮助"
					}
				},
				{
					"path": "my/helpDetail",
					"style": {
						"navigationBarTitleText": "帮助中心"
					}
				},

				// {
				// 	"path": "my/addManagement",
				// 	"style": {
				// 		"navigationBarTitleText": "新增课程"
				// 	}
				// },
				// {
				// 	"path": "my/mentList",
				// 	"style": {
				// 		"navigationBarTitleText": "订单管理"
				// 	}
				// },

				// {
				// 	"path": "my/refund",
				// 	"style": {
				// 		"navigationBarTitleText": "发起退款"
				// 	}
				// },
				{
					"path": "center/detail",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom" //单个页面设置
					}
				},
				{
					"path": "center/detailSys",
					"style": {
						"navigationBarTitleText": "", // 发布活动详情
						"navigationStyle": "custom"
					}
				},
				{
					"path": "center/signDetail",
					"style": {
						"navigationBarTitleText": "报名信息"
					}
				},
				// {
				// 	"path": "center/MechanismDetail",
				// 	"style": {
				// 		"navigationBarTitleText": "机构详情"
				// 	}
				// },

				// {
				// 	"path": "center/orderDetail",
				// 	"style": {
				// 		"navigationBarTitleText": "订单详情"
				// 	}
				// },
				{
					"path": "center/applyDetail",
					"style": {
						"navigationBarTitleText": "支付参加"
					}
				},
				{
					"path": "cropper",
					"style": {
						"navigationBarTitleText": "头像剪裁"
					}
				}
			]

		},
		{
			"root": "packageB",
			"pages": [{
					"path": "names/index",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				}, {
					"path": "wallet/index",
					"style": {
						"navigationBarTitleText": "我的钱包"
					}
				}, {
					"path": "wallet/bank",
					"style": {
						"navigationBarTitleText": "提现"
					}
				}, {
					"path": "wallet/add_bank",
					"style": {
						"navigationBarTitleText": "银行卡信息"
					}
				}, {
					"path": "card/index",
					"style": {
						"navigationBarTitleText": "身份信息"
					}
				}, {
					"path": "team/index",
					"style": {
						"navigationBarTitleText": "添加报名人"
					}
				}, {
					"path": "privacy",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "editAct",
					"style": {
						"navigationBarTitleText": "编辑活动信息"
					}
				},
				{
					"path": "editDraft",
					"style": {
						"navigationBarTitleText": "编辑草稿箱"
					}
				},
				{
					"path": "invoice/invoiceCenter",
					"style": {
						"navigationBarTitleText": "发票中心"
					}
				},
				{
					"path": "invoice/addInvoice",
					"style": {
						"navigationBarTitleText": "填写发票信息"
					}
				},
				{
					"path": "invoice/invoiceInfo",
					"style": {
						"navigationBarTitleText": "查看发票"
					}
				},
				{
					"path": "outWeb",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "invoice/addHead",
					"style": {
						"navigationBarTitleText": "添加发票抬头信息"
					}
				},
				{
					"path": "club/info",
					"style": {
						"navigationBarTitleText": "俱乐部详情"
					}
				}
			]
		}
	],
	"preloadRule": { // 分包预载配置 
		"pages/index/index": { // 在进入小程序pages/index页面时，由框架自动预下载分包A、分包B
			"network": "all",
			"packages": ["packageA"]
		}
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#F5F5F5"
	},
	"tabBar": {
		"custom": true,
		"color": "#888888",
		"selectedColor": "#181818",
		"list": [{
				"pagePath": "pages/index/index",
				"text": "首页"
			},
			{
				"pagePath": "pages/fenl/index", // 更新为与 pages 数组一致
				"text": "分类"
			},
			{
				"pagePath": "pages/center/index", // 更新为与 pages 数组一致
				"text": "发布活动"
			},
			{
				"pagePath": "pages/activity/index",
				"text": "活动中心"
			},
			{
				"pagePath": "pages/my/index",
				"text": "我的"

			}

		]
	},
	"__usePrivacyCheck__": true,
	"uniIdRouter": {}
}